/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\nconst GET = handler;\nconst POST = handler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnQztBQUNRO0FBRXhDLE1BQU1FLFVBQVVGLGdEQUFRQSxDQUFDQyxrREFBV0E7QUFFN0IsTUFBTUUsTUFBTUQsUUFBTztBQUNuQixNQUFNRSxPQUFPRixRQUFPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXGNvZGVzXFxpbm5vLWNybVxcaW5uby1jcm0tc3RhZmZcXGFwcFxcYXBpXFxhdXRoXFxbLi4ubmV4dGF1dGhdXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmV4dEF1dGggZnJvbSBcIm5leHQtYXV0aFwiXHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSBcIkAvbGliL2F1dGhcIlxyXG5cclxuY29uc3QgaGFuZGxlciA9IE5leHRBdXRoKGF1dGhPcHRpb25zKVxyXG5cclxuZXhwb3J0IGNvbnN0IEdFVCA9IGhhbmRsZXJcclxuZXhwb3J0IGNvbnN0IFBPU1QgPSBoYW5kbGVyXHJcbiJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsImF1dGhPcHRpb25zIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/inter-server */ \"(rsc)/./lib/inter-server.ts\");\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                phone: {\n                    label: \"Phone\",\n                    type: \"text\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.phone || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Staff server: Authenticate against admin server via inter-server communication\n                    const authResult = await _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.StaffServerAPI.authenticateUser(credentials.phone, credentials.password);\n                    if (!authResult.success) {\n                        console.error('Authentication failed:', authResult.error);\n                        return null;\n                    }\n                    const user = authResult.data.user;\n                    if (!user) {\n                        console.error('No user data returned from admin server');\n                        return null;\n                    }\n                    // Verify the user role is allowed on staff server\n                    const allowedRoles = [\n                        'RECEPTION',\n                        'ACADEMIC_MANAGER',\n                        'TEACHER',\n                        'MANAGER'\n                    ];\n                    if (!allowedRoles.includes(user.role)) {\n                        console.error('User role not allowed on staff server:', user.role);\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        phone: user.phone,\n                        name: user.name,\n                        email: user.email,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Error authenticating user via inter-server:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role || null;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/inter-server.ts":
/*!*****************************!*\
  !*** ./lib/inter-server.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminServerAPI: () => (/* binding */ AdminServerAPI),\n/* harmony export */   InterServerUtils: () => (/* binding */ InterServerUtils),\n/* harmony export */   StaffServerAPI: () => (/* binding */ StaffServerAPI),\n/* harmony export */   createInterServerHeaders: () => (/* binding */ createInterServerHeaders),\n/* harmony export */   makeInterServerRequest: () => (/* binding */ makeInterServerRequest),\n/* harmony export */   validateInterServerAuth: () => (/* binding */ validateInterServerAuth),\n/* harmony export */   withInterServerAuth: () => (/* binding */ withInterServerAuth)\n/* harmony export */ });\n// Inter-Server Communication Library\n// Handles secure communication between admin and staff servers\n/**\n * Validates inter-server request authentication\n */ function validateInterServerAuth(request) {\n    const authHeader = request.headers.get('X-Inter-Server-Secret');\n    const expectedSecret = process.env.INTER_SERVER_SECRET;\n    if (!authHeader || !expectedSecret) {\n        return false;\n    }\n    return authHeader === expectedSecret;\n}\n/**\n * Creates authenticated headers for inter-server requests\n */ function createInterServerHeaders() {\n    const secret = process.env.INTER_SERVER_SECRET;\n    if (!secret) {\n        throw new Error('INTER_SERVER_SECRET not configured');\n    }\n    return {\n        'Content-Type': 'application/json',\n        'X-Inter-Server-Secret': secret,\n        'User-Agent': `${process.env.SERVER_TYPE || 'unknown'}-server`\n    };\n}\n/**\n * Makes authenticated request to another server\n */ async function makeInterServerRequest(targetServer, request) {\n    try {\n        const baseUrl = targetServer === 'admin' ? process.env.ADMIN_SERVER_URL : process.env.STAFF_SERVER_URL;\n        if (!baseUrl) {\n            throw new Error(`${targetServer.toUpperCase()}_SERVER_URL not configured`);\n        }\n        const url = `${baseUrl}${request.endpoint}`;\n        const headers = {\n            ...createInterServerHeaders(),\n            ...request.headers\n        };\n        const response = await fetch(url, {\n            method: request.method,\n            headers,\n            body: request.data ? JSON.stringify(request.data) : undefined\n        });\n        const responseData = await response.json();\n        return {\n            success: response.ok,\n            data: responseData,\n            status: response.status,\n            error: response.ok ? undefined : responseData.error || 'Request failed'\n        };\n    } catch (error) {\n        return {\n            success: false,\n            status: 500,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\n * Middleware for protecting inter-server endpoints\n */ function withInterServerAuth(handler) {\n    return async (request, ...args)=>{\n        if (!validateInterServerAuth(request)) {\n            return new Response(JSON.stringify({\n                error: 'Unauthorized inter-server request'\n            }), {\n                status: 401,\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n        }\n        return handler(request, ...args);\n    };\n}\n/**\n * Staff server functions - for requesting data from admin server\n */ class StaffServerAPI {\n    /**\n   * Request user authentication from admin server\n   */ static async authenticateUser(phone, password) {\n        return makeInterServerRequest('admin', {\n            endpoint: '/api/inter-server/auth/validate',\n            method: 'POST',\n            data: {\n                phone,\n                password\n            }\n        });\n    }\n    /**\n   * Get user data from admin server\n   */ static async getUserData(userId) {\n        return makeInterServerRequest('admin', {\n            endpoint: `/api/inter-server/users/${userId}`,\n            method: 'GET'\n        });\n    }\n    /**\n   * Sync data with admin server\n   */ static async syncData(dataType, data) {\n        return makeInterServerRequest('admin', {\n            endpoint: '/api/inter-server/sync',\n            method: 'POST',\n            data: {\n                type: dataType,\n                data\n            }\n        });\n    }\n}\n/**\n * Admin server functions - for handling requests from staff server\n */ class AdminServerAPI {\n    /**\n   * Validate staff server request\n   */ static async validateStaffRequest(request) {\n        return validateInterServerAuth(request);\n    }\n    /**\n   * Send data to staff server\n   */ static async sendToStaff(endpoint, data) {\n        return makeInterServerRequest('staff', {\n            endpoint,\n            method: 'POST',\n            data\n        });\n    }\n    /**\n   * Broadcast update to staff server\n   */ static async broadcastUpdate(updateType, data) {\n        return makeInterServerRequest('staff', {\n            endpoint: '/api/inter-server/updates',\n            method: 'POST',\n            data: {\n                type: updateType,\n                data\n            }\n        });\n    }\n}\n/**\n * Common utilities for both servers\n */ class InterServerUtils {\n    /**\n   * Log inter-server communication\n   */ static logRequest(direction, endpoint, success, details) {\n        const timestamp = new Date().toISOString();\n        const serverType = process.env.SERVER_TYPE || 'unknown';\n        console.log(`[${timestamp}] Inter-Server ${direction.toUpperCase()}: ${endpoint}`, {\n            server: serverType,\n            success,\n            details\n        });\n    }\n    /**\n   * Check if current server can communicate with target server\n   */ static async healthCheck(targetServer) {\n        try {\n            const response = await makeInterServerRequest(targetServer, {\n                endpoint: '/api/health',\n                method: 'GET'\n            });\n            return response.success;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * Get server configuration\n   */ static getServerConfig() {\n        return {\n            serverType: process.env.SERVER_TYPE,\n            adminUrl: process.env.ADMIN_SERVER_URL,\n            staffUrl: process.env.STAFF_SERVER_URL,\n            hasInterServerSecret: !!process.env.INTER_SERVER_SECRET\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/inter-server.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_staff_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm\\\\inno-crm-staff\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_inno_crm_inno_crm_staff_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGJTVCLi4ubmV4dGF1dGglNUQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmF1dGglMkYlNUIuLi5uZXh0YXV0aCU1RCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmF1dGglMkYlNUIuLi5uZXh0YXV0aCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNXaW5kb3dzJTIwMTElNUNEZXNrdG9wJTVDY29kZXMlNUNpbm5vLWNybSU1Q2lubm8tY3JtLXN0YWZmJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNXaW5kb3dzJTIwMTElNUNEZXNrdG9wJTVDY29kZXMlNUNpbm5vLWNybSU1Q2lubm8tY3JtLXN0YWZmJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUN3RDtBQUNySTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEZXNrdG9wXFxcXGNvZGVzXFxcXGlubm8tY3JtXFxcXGlubm8tY3JtLXN0YWZmXFxcXGFwcFxcXFxhcGlcXFxcYXV0aFxcXFxbLi4ubmV4dGF1dGhdXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hdXRoL1suLi5uZXh0YXV0aF1cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXFdpbmRvd3MgMTFcXFxcRGVza3RvcFxcXFxjb2Rlc1xcXFxpbm5vLWNybVxcXFxpbm5vLWNybS1zdGFmZlxcXFxhcHBcXFxcYXBpXFxcXGF1dGhcXFxcWy4uLm5leHRhdXRoXVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Cinno-crm%5Cinno-crm-staff&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
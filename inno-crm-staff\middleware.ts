import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// Define protected routes and their required roles
const protectedRoutes = {
  '/dashboard': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER'],
  '/dashboard/analytics': ['ADMIN'], // ADMIN ONLY for financial data
  '/dashboard/users': ['ADMIN'],
  '/dashboard/teachers': ['ADMIN', 'MANAGER'],
  '/dashboard/students': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER'], // Added CASHIER
  '/dashboard/groups': ['ADMIN', 'MANAGER', 'TEACHER'],
  '/dashboard/enrollments': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/dashboard/payments': ['ADMIN', 'CASHIER'], // ADMIN and CAS<PERSON>IE<PERSON> only
  '/dashboard/attendance': ['MANAGER', 'TEACHER'], // Removed ADMIN access - they use analytics instead
  '/dashboard/assessments': ['ADMIN', 'MANAGER', 'TEACHER', 'ACADEMIC_MANAGER'], // Added ACADEMIC_MANAGER access
  '/dashboard/classes': ['ADMIN', 'MANAGER', 'TEACHER'],
  '/dashboard/leads': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/dashboard/communication': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION'],
}

// API routes that require authentication
const protectedApiRoutes = {
  '/api/analytics': ['ADMIN'], // ADMIN ONLY for financial analytics
  '/api/reports': ['ADMIN'], // ADMIN ONLY for financial reports
  '/api/users': ['ADMIN'],
  '/api/teachers': ['ADMIN', 'MANAGER'],
  '/api/students': ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER'], // Added CASHIER, STUDENT, ACADEMIC_MANAGER for attendance access
  '/api/groups': ['ADMIN', 'MANAGER', 'TEACHER'],
  '/api/enrollments': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/api/payments': ['ADMIN', 'CASHIER'], // ADMIN and CASHIER only
  '/api/attendance': ['MANAGER', 'TEACHER'], // Removed ADMIN access - they use analytics instead
  '/api/assessments': ['ADMIN', 'MANAGER', 'TEACHER', 'ACADEMIC_MANAGER'], // Added ACADEMIC_MANAGER access
  '/api/leads': ['ADMIN', 'MANAGER', 'RECEPTION'],
  '/api/courses': ['ADMIN', 'MANAGER'],
}

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/api/health', // Allow health check endpoint
  '/api/leads', // Allow public lead submission
  '/api/auth/verify', // Allow inter-server authentication verification
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/static') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon')
  ) {
    return NextResponse.next()
  }

  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route === pathname) return true
    if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true
    return false
  })

  // Allow public routes
  if (isPublicRoute) {
    return NextResponse.next()
  }

  // Get the token from the request
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  })

  // Redirect to signin if no token
  if (!token) {
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // Check role-based access for protected routes
  const userRole = token.role as string

  // Admin server: Only allow ADMIN and CASHIER roles
  const serverType = process.env.SERVER_TYPE || 'admin'
  if (serverType === 'admin') {
    const allowedRoles = ['ADMIN', 'CASHIER']
    if (!allowedRoles.includes(userRole)) {
      return NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url))
    }
  }

  // Check dashboard routes
  for (const [route, allowedRoles] of Object.entries(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      if (!allowedRoles.includes(userRole)) {
        return NextResponse.redirect(new URL('/dashboard/unauthorized', request.url))
      }
      break
    }
  }

  // Check API routes
  for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)) {
    if (pathname.startsWith(route)) {
      if (!allowedRoles.includes(userRole)) {
        return NextResponse.json(
          { error: 'Unauthorized access' },
          { status: 403 }
        )
      }
      break
    }
  }

  // Special handling for student access
  if (userRole === 'STUDENT') {
    // Students can only access their own data
    const userId = token.sub

    // Allow access to student dashboard
    if (pathname.startsWith('/dashboard/student')) {
      return NextResponse.next()
    }

    // Restrict access to other dashboard routes
    if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {
      return NextResponse.redirect(new URL('/dashboard/student', request.url))
    }
  }

  // Special handling for academic manager access
  if (userRole === 'ACADEMIC_MANAGER') {
    // Academic managers have access to assessments and test statistics
    const allowedPaths = ['/dashboard', '/dashboard/assessments', '/dashboard/students']
    const isAllowed = allowedPaths.some(path => pathname.startsWith(path))

    if (!isAllowed && pathname.startsWith('/dashboard')) {
      return NextResponse.redirect(new URL('/dashboard/assessments', request.url))
    }
  }

  // Teacher-specific restrictions
  if (userRole === 'TEACHER') {
    // Teachers can access their assigned groups and students
    if (pathname.startsWith('/dashboard/teacher')) {
      return NextResponse.next()
    }
  }

  // Reception-specific restrictions
  if (userRole === 'RECEPTION') {
    // Reception can access leads, students, and enrollments
    const allowedPaths = ['/dashboard', '/dashboard/leads', '/dashboard/students', '/dashboard/enrollments']
    if (!allowedPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }

  // Cashier-specific restrictions
  if (userRole === 'CASHIER') {
    // Cashiers can ONLY access payments and basic student info - NO financial analytics
    const allowedPaths = ['/dashboard', '/dashboard/payments', '/dashboard/students']
    if (!allowedPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    // Block access to any financial analytics or reports
    if (pathname.includes('/analytics') || pathname.includes('/reports')) {
      return NextResponse.redirect(new URL('/dashboard/unauthorized', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
